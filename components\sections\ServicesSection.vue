<template>
  <SectionContainer
    :id="id"
    :title="title"
    :subtitle="subtitle"
    :variant="variant"
    :size="size"
  >
    <div :class="gridClasses">
      <Card
        v-for="(service, index) in services"
        :key="index"
        :variant="cardVariant"
        :size="cardSize"
        :image-url="service.image"
        :image-alt="service.imageAlt"
        :title="service.title"
        :description="service.description"
        :price="service.price"
        :price-unit="service.priceUnit"
        :badge="service.badge"
        :hover="true"
      >
        <template v-if="service.features && service.features.length" #content>
          <ul class="space-y-2 mb-4">
            <li
              v-for="(feature, featureIndex) in service.features"
              :key="featureIndex"
              class="flex items-center text-sm text-primary/80"
            >
              <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
              {{ feature }}
            </li>
          </ul>
        </template>
        
        <template v-if="showActions" #actions>
          <div class="flex flex-col sm:flex-row gap-2">
            <Button
              v-if="service.primaryAction"
              :variant="service.primaryAction.variant || 'primary'"
              :size="actionButtonSize"
              :icon="service.primaryAction.icon"
              :href="service.primaryAction.href"
              :to="service.primaryAction.to"
              :full-width="true"
              @click="handleServiceAction(service, 'primary')"
            >
              {{ service.primaryAction.text }}
            </Button>

            <Button
              v-if="service.secondaryAction"
              :variant="service.secondaryAction.variant || 'outline'"
              :size="actionButtonSize"
              :icon="service.secondaryAction.icon"
              :href="service.secondaryAction.href"
              :to="service.secondaryAction.to"
              :full-width="true"
              @click="handleServiceAction(service, 'secondary')"
            >
              {{ service.secondaryAction.text }}
            </Button>
          </div>
        </template>
      </Card>
    </div>
    
    <!-- Optional CTA at bottom -->
    <div v-if="bottomCta" class="text-center mt-12">
      <Button
        :variant="bottomCta.variant || 'primary'"
        size="lg"
        :icon="bottomCta.icon"
        :href="bottomCta.href"
        :to="bottomCta.to"
        @click="handleBottomCta"
      >
        {{ bottomCta.text }}
      </Button>
    </div>
  </SectionContainer>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import Card from '../ui/Card.vue'
import Button from '../ui/Button.vue'
import SectionContainer from '../ui/SectionContainer.vue'

interface ServiceAction {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  icon?: string
  href?: string
  to?: string
}

interface Service {
  title: string
  description: string
  image?: string
  imageAlt?: string
  price?: string
  priceUnit?: string
  badge?: string
  features?: string[]
  primaryAction?: ServiceAction
  secondaryAction?: ServiceAction
}

interface BottomCta {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  icon?: string
  href?: string
  to?: string
}

interface Props {
  id?: string
  title?: string
  subtitle?: string
  services: Service[]
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  columns?: 1 | 2 | 3 | 4
  cardVariant?: 'default' | 'elevated' | 'outlined' | 'ghost'
  cardSize?: 'sm' | 'md' | 'lg'
  showActions?: boolean
  actionButtonSize?: 'sm' | 'md' | 'lg'
  bottomCta?: BottomCta
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  columns: 3,
  cardVariant: 'elevated',
  cardSize: 'md',
  showActions: true,
  actionButtonSize: 'md'
})

const emit = defineEmits<{
  serviceAction: [service: Service, actionType: 'primary' | 'secondary']
  bottomCta: []
}>()

const gridClasses = computed(() => {
  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  }
  
  return `grid ${columnClasses[props.columns]} gap-6 lg:gap-8`
})

const handleServiceAction = (service: Service, actionType: 'primary' | 'secondary') => {
  emit('serviceAction', service, actionType)
}

const handleBottomCta = () => {
  emit('bottomCta')
}
</script>
