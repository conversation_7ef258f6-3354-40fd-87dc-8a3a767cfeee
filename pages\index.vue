<template>
  <div>
    <!-- Hero Section -->
    <HeroSection
      :title="(data as any)?.meta?.hero?.title || 'Your Business Name'"
      :subtitle="(data as any)?.meta?.hero?.subtitle || 'Professional Services You Can Trust'"
      :description="(data as any)?.meta?.hero?.description || 'We provide exceptional services tailored to your needs. Experience quality, reliability, and excellence in everything we do.'"
      :background-image="(data as any)?.meta?.hero?.backgroundImage"
      :primary-cta="{
        text: 'Get Started',
        href: '#contact',
        icon: 'heroicons:arrow-right'
      }"
      :secondary-cta="{
        text: 'Learn More',
        href: '#about',
        icon: 'heroicons:information-circle'
      }"
      :features="(data as any)?.hero?.features || [
        { icon: 'heroicons:star', title: 'Quality Service', description: 'Top-notch quality in everything we do' },
        { icon: 'heroicons:clock', title: 'Fast Delivery', description: 'Quick turnaround times' },
        { icon: 'heroicons:shield-check', title: 'Trusted', description: 'Reliable and trustworthy service' }
      ]"
      @primary-cta="scrollToSection('contact')"
      @secondary-cta="scrollToSection('about')"
    />

    <!-- About Section -->
    <AboutSection
      id="about"
      :title="(data as any)?.about?.title || 'About Us'"
      :subtitle="(data as any)?.about?.subtitle || 'Your trusted partner for exceptional service'"
      :description="(data as any)?.about?.description || 'With years of experience in the industry, we have built a reputation for delivering outstanding results. Our commitment to quality and customer satisfaction sets us apart from the competition.'"
      :image="(data as any)?.about?.image"
      :stats="(data as any)?.about?.stats || [
        { value: '500+', label: 'Happy Clients' },
        { value: '5+', label: 'Years Experience' },
        { value: '1000+', label: 'Projects Completed' },
        { value: '24/7', label: 'Support' }
      ]"
      :features="(data as any)?.about?.features || [
        { icon: 'heroicons:check-circle', title: 'Professional Team', description: 'Experienced professionals dedicated to your success' },
        { icon: 'heroicons:check-circle', title: 'Quality Guarantee', description: 'We stand behind our work with a satisfaction guarantee' },
        { icon: 'heroicons:check-circle', title: 'Competitive Pricing', description: 'Fair and transparent pricing for all our services' }
      ]"
      :cta="{
        text: 'Contact Us Today',
        icon: 'heroicons:phone',
        href: '#contact'
      }"
      :owner="(data as any)?.about?.owner || {
        name: 'John Doe',
        title: 'Founder & CEO',
        bio: 'With over 10 years of experience in the industry, John founded this company with a vision to provide exceptional service to every client.',
        image: '/owner-photo.jpg'
      }"
    />

    <!-- Services Section -->
    <ServicesSection
      id="services"
      :title="(data as any)?.services?.title || 'Our Services'"
      :subtitle="(data as any)?.services?.subtitle || 'What we offer to help you succeed'"
      :services="servicesData"
      variant="secondary"
      @service-action="handleServiceAction"
    />



    <!-- Products Section -->
    <ProductsSection
      id="products"
      :title="(data as any)?.products?.title || 'Our Products'"
      :subtitle="(data as any)?.products?.subtitle || 'Discover our range of premium products and solutions'"
      :products="productsData"
      :categories="(data as any)?.products?.categories"
      :currency="(data as any)?.products?.currency || '$'"
      :show-stock="(data as any)?.products?.showStock || false"
      :show-quick-actions="(data as any)?.products?.showQuickActions || false"
      :compare-enabled="(data as any)?.products?.compareEnabled || false"
      :default-primary-action="(data as any)?.products?.defaultPrimaryAction"
      :default-secondary-action="(data as any)?.products?.defaultSecondaryAction"
      variant="secondary"
      @product-click="handleProductClick"
      @product-action="handleProductAction"
      @wishlist="handleWishlist"
      @share="handleShare"
      @compare="handleCompare"
    />

    <!-- Gallery Section -->
    <section id="gallery" class="py-20 px-4 sm:px-6 lg:px-8 bg-tertiary/30">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl sm:text-4xl font-bold text-primary mb-4">{{ (data as any)?.gallery?.title || 'Our Work' }}</h2>
          <p class="text-lg text-muted">{{ (data as any)?.gallery?.subtitle || 'See what we\'ve accomplished for our clients' }}</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64 border border-primary/10 hover:border-primary/30 transition-all duration-300">
            <div class="w-full h-full bg-gradient-to-br from-primary/10 to-tertiary/60 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-muted text-sm">Beautiful project completed for client</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/20 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-tertiary/60 to-primary/10 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-muted text-sm">Another successful project</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-primary/30 to-secondary/30 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Quality work delivered on time</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-secondary/30 to-primary/40 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Professional results</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-primary/25 to-secondary/35 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Satisfied customer</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-secondary/25 to-primary/35 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Excellence in every detail</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 px-4 sm:px-6 lg:px-8 bg-primary text-secondary">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl sm:text-4xl font-bold mb-4 text-secondary">{{ (data as any)?.contact?.title || 'Get In Touch' }}</h2>
          <p class="text-lg text-secondary/80">{{ (data as any)?.contact?.subtitle || 'Ready to start your project? Contact us today!' }}</p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12">
          <!-- Contact Information -->
          <div class="space-y-8">
            <div class="space-y-6">
              <!-- Address -->
              <ContactInfo
                icon="heroicons:map-pin"
                title="Address"
                :text="(data as any)?.contact?.address || '123 Business Street, City, State 12345'"
                variant="light"
              />

              <!-- Phone -->
              <ContactInfo
                icon="heroicons:phone"
                title="Phone"
                :link="{
                  href: `tel:${(data as any)?.contact?.phone || '+****************'}`,
                  text: (data as any)?.contact?.phone || '+****************'
                }"
                variant="light"
              />

              <!-- Email -->
              <ContactInfo
                icon="heroicons:envelope"
                title="Email"
                :link="{
                  href: `mailto:${(data as any)?.contact?.email || '<EMAIL>'}`,
                  text: (data as any)?.contact?.email || '<EMAIL>'
                }"
                variant="light"
              />

              <!-- Hours -->
              <ContactInfo
                icon="heroicons:clock"
                title="Hours"
                variant="light"
              >
                <div class="space-y-1">
                  <div
                    v-for="(hour, index) in (data as any)?.contact?.hours || [
                      { day: 'Monday - Friday', time: '9:00 AM - 6:00 PM' },
                      { day: 'Saturday', time: '10:00 AM - 4:00 PM' },
                      { day: 'Sunday', time: 'Closed' }
                    ]"
                    :key="index"
                    class="flex justify-between space-x-8 text-sm opacity-80"
                  >
                    <span>{{ hour.day }}</span>
                    <span>{{ hour.time }}</span>
                  </div>
                </div>
              </ContactInfo>
            </div>

            <!-- Social Links -->
            <div class="pt-6 border-t border-secondary/20">
              <h3 class="font-semibold mb-4">Follow Us</h3>
              <div class="flex space-x-4">
                <a
                  v-for="(social, index) in (data as any)?.contact?.socialLinks || [
                    { icon: 'simple-icons:facebook', url: 'https://facebook.com/yourbusiness' },
                    { icon: 'simple-icons:instagram', url: 'https://instagram.com/yourbusiness' },
                    { icon: 'simple-icons:twitter', url: 'https://twitter.com/yourbusiness' }
                  ]"
                  :key="index"
                  :href="social.url"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="w-10 h-10 bg-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors"
                >
                  <Icon :name="social.icon" class="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="space-y-6">
            <Card variant="elevated" size="lg" :hover="false">
              <template #title>
                <h3 class="text-xl font-bold text-primary">Send us a message</h3>
              </template>

              <template #content>
                <form @submit.prevent="handleContactForm" class="space-y-4">
                  <div class="grid md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-primary mb-2">Name</label>
                      <input
                        v-model="contactForm.name"
                        type="text"
                        required
                        class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary text-primary placeholder-muted"
                      >
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-primary mb-2">Phone</label>
                      <input
                        v-model="contactForm.phone"
                        type="tel"
                        class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                      >
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-primary mb-2">Email</label>
                    <input
                      v-model="contactForm.email"
                      type="email"
                      required
                      class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                    >
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-primary mb-2">Message</label>
                    <textarea
                      v-model="contactForm.message"
                      rows="4"
                      required
                      class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary resize-none"
                    ></textarea>
                  </div>

                  <Button type="submit" variant="primary" size="lg" full-width>
                    Send Message
                  </Button>
                </form>
              </template>
            </Card>
          </div>
        </div>
      </div>
    </section>

    <!-- Floating Action Buttons -->
    <ClientOnly>
      <FloatingActions
        :whatsapp-number="(data as any)?.meta?.contact?.whatsapp || '+1555123456'"
        whatsapp-message="Hello! I'm interested in your services. Can you help me?"
      />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import Card from '~/components/ui/Card.vue'
import FloatingActions from '~/components/ui/FloatingActions.vue'
import Button from '~/components/ui/Button.vue'
import ContactInfo from '~/components/ui/ContactInfo.vue'
import HeroSection from '~/components/sections/HeroSection.vue'
import AboutSection from '~/components/sections/AboutSection.vue'
import ServicesSection from '~/components/sections/ServicesSection.vue'
import ProductsSection from '~/components/sections/ProductsSection.vue'

// Fetch content from Nuxt Content using new v3.6 syntax
const { data: data } = await useAsyncData('main-content', () =>
  queryCollection('content').path('/').first()
)

const { data: services } = await useAsyncData('services', () =>
  queryCollection('services').all()
)

const { data: products } = await useAsyncData('products', () =>
  queryCollection('products').all()
)





const servicesData = computed(() => (services.value || []).sort((a: any, b: any) => (a.order || 0) - (b.order || 0)))
const productsData = computed(() => (products.value || []).sort((a: any, b: any) => (a.order || 0) - (b.order || 0)))

// Utility functions
const scrollToSection = (sectionId: string) => {
  if (typeof window !== 'undefined') {
    const element = document.getElementById(sectionId)
    if (element) {
      // Calculate offset for fixed navbar (navbar height + some padding)
      const isMobile = window.innerWidth < 768
      const navbarHeight = isMobile ? 80 : 96 // Mobile: 80px, Desktop: 96px
      const elementPosition = element.offsetTop - navbarHeight

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      })
    }
  }
}



// Event handlers
const handleServiceAction = (service: any, actionType: 'primary' | 'secondary') => {
  console.log(`Service action clicked:`, service.title, actionType)
  if (actionType === 'primary') {
    scrollToSection('contact')
  }
}

// Product event handlers
const handleProductClick = (product: any) => {
  console.log('Product clicked:', product.name)
  // Add your product click logic here
}

const handleProductAction = (product: any, actionType: 'primary' | 'secondary') => {
  console.log(`Product action clicked:`, product.name, actionType)
  if (actionType === 'primary') {
    scrollToSection('contact')
  }
}

const handleWishlist = (product: any) => {
  console.log('Added to wishlist:', product.name)
  // Add your wishlist logic here
}

const handleShare = (product: any) => {
  console.log('Share product:', product.name)
  // Add your share logic here
  if (navigator.share) {
    navigator.share({
      title: product.name,
      text: product.description,
      url: window.location.href
    })
  }
}

const handleCompare = (product: any) => {
  console.log('Compare product:', product.name)
  // Add your compare logic here
}

// Contact form
const contactForm = ref({
  name: '',
  phone: '',
  email: '',
  message: ''
})

const handleContactForm = () => {
  console.log('Contact form submitted:', contactForm.value)
  alert('Thank you for your message! We will get back to you soon.')

  // Reset form
  contactForm.value = {
    name: '',
    phone: '',
    email: '',
    message: ''
  }
}

// SEO
useSeoMeta({
  title: computed(() => data?.value?.seo?.title || 'Your Business Name - Professional Services'),
  ogTitle: computed(() => data?.value?.seo?.title || 'Your Business Name - Professional Services'),
  description: computed(() => data?.value?.seo?.description || 'Professional services you can trust.'),
  ogDescription: computed(() => data?.value?.seo?.description || 'Professional services you can trust.'),
  ogType: 'website'
})
</script>
