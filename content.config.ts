import { defineContentConfig, defineCollection } from "@nuxt/content";

export default defineContentConfig({
  collections: {
    content: defineCollection({
        type: 'page',
        source: '**/*.md'
    }),
    services: defineCollection({
        type: 'page',
        source: 'services/*.md'
    }),
    products: defineCollection({
        type: 'page',
        source: 'products/*.md'
    })
  },
});
